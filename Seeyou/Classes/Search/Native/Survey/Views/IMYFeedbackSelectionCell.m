//
//  IMYFeedbackSelectionCell.m
//  zzimymain
//
//

#import "IMYFeedbackSelectionCell.h"
#import <IMYBaseKit/IMYBaseKit.h>

@interface IMYFeedbackSelectionCell ()

@property (nonatomic, strong) IMYTouchEXButton *button;
@property (nonatomic, strong) IMYSurveyOptionModel *currentOption;

@end

@implementation IMYFeedbackSelectionCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.button = [IMYTouchEXButton buttonWithType:UIButtonTypeCustom];
    self.button.titleLabel.font = [UIFont systemFontOfSize:14];
    self.button.titleLabel.lineBreakMode = UILineBreakModeTailTruncation;
    self.button.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
    self.button.contentEdgeInsets = UIEdgeInsetsMake(10, 12, 10, 12);
    self.button.layer.cornerRadius = 8; // 圆角半径8
    self.button.layer.masksToBounds = YES;
    
    // 禁用按钮的用户交互，交互由 Cell 的 didSelectItemAtIndexPath 处理
    self.button.userInteractionEnabled = NO;
    
    [self.contentView addSubview:self.button];
    
    // 设置约束，让按钮填满整个 Cell
    [self.button mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self.contentView);
    }];
}

- (void)configureWithOption:(IMYSurveyOptionModel *)option {
    self.currentOption = option;
    
    // 设置按钮文本
    [self.button imy_setTitle:option.text];
    
    // 更新选中状态
    [self updateSelectionState:option.local_isSelected];
}

- (void)updateSelectionState:(BOOL)isSelected {
    if (self.currentOption) {
        self.currentOption.local_isSelected = isSelected;
    }
    
    if (isSelected) {
        [self.button imy_setTitleColor:kCK_Red_A];
        [self.button imy_setBackgroundColor:@"#FFDBE7"];
    } else {
        [self.button imy_setTitleColor:kCK_Black_A];
        [self.button imy_setBackgroundColor:kCK_Black_FN];
    }
}

- (void)prepareForReuse {
    [super prepareForReuse];
    
    // 重置状态
    self.currentOption = nil;
    [self.button imy_setTitle:@""];
    [self updateSelectionState:NO];
}

@end
