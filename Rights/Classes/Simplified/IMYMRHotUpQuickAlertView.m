//
//  IMYMRHotUpQuickAlertView.m
//  ZZIMYMain
//
//  Created by ljh on 2025/7/25.
//

#import "IMYMRHotUpQuickAlertView.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import <IMYBaseKit/IMYAlertShowAutoRobot.h>
#import <IOC-Protocols/IOCAppInfo.h>
#import <IMYAdvertisement/IMYAdSplashManager.h>

@interface IMYMRHotUpQuickAlertView () <IMYAlertShowViewProtocol>

@property (nonatomic, strong) UIImageView *iconView;

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *subtitleLabel;

@property (nonatomic, strong) IMYTouchEXButton *actionButton;

@property (nonatomic, strong) NSDictionary *planData;
@property (nonatomic, strong) NSDictionary *popupInfo;

@end

#define kShowTopY 42

@implementation IMYMRHotUpQuickAlertView

IMY_KYLIN_FUNC_MAINTAB_ASYNC {
    // 1秒后注册弹窗 + 限流器2秒，整体会比青少年弹窗(3秒)慢一点
    imy_asyncMainBlock(1, ^{
        [IMYMRHotUpQuickAlertView registQuickAlert];
    });
}

+ (void)registQuickAlert {
    // 首次启动
    RACSignal *appSetup = [RACSignal return:@1];
    // 二次启动
    RACSignal *appResume = [[NSNotificationCenter defaultCenter] rac_addObserverForName:UIApplicationWillEnterForegroundNotification object:nil];
    // 切换身份+登录
    RACSignal *modeSignal = [[IMYPublicAppHelper shareAppHelper].userModeChangedSignal map:^id _Nullable(id  _Nullable value) {
        // 先关闭正在显示的弹窗
        imy_asyncMainExecuteBlock(^{
            [self dissmissShowAlert];
        });
        return value;
    }];
    // 聚合监听
    [[RACSignal merge:@[appSetup, appResume, modeSignal]] subscribeNext:^(id  _Nullable x) {
        // 限流器：2s
        imy_throttle_on_queue(2, @"IMYMRHotUpQuickAlertView", dispatch_get_main_queue(), ^{
            [self loadData];
        });
    }];
}

+ (void)loadData {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    // 当前孕周
    NSInteger const currentPregnenyWeek = IMYHIVE_BINDER(IOCAppInfo).currentPregnenyWeek;
    if (currentPregnenyWeek >= 0) {
        params[@"pregnancy_week"] = @(currentPregnenyWeek);
    }
    // 当前最小宝宝出生日
    NSString * const birthdayString = [IMYHIVE_BINDER(IOCAppInfo).currentLatestBabyBirthday imy_getOnlyDateString];
    if (birthdayString.length > 0) {
        params[@"min_baby_birthday"] = birthdayString;
    }
    // 经期/备孕 阶段
    NSNumber * const stageType = [[IMYURIManager sharedInstance] runActionAndSyncResultWithPath:@"chatAI/stageType" params:nil];
    if (stageType) {
        params[@"stage_type"] = stageType;
    }
    
    [[IMYServerRequest getPath:@"api/v3/top_popups" host:sub_seeyouyima_com params:params headers:nil] subscribeNext:^(IMYHTTPResponse *x) {
        NSDictionary *frequencyData = x.responseObject[@"frequency"];
        if (![self shouldShowWithGlobalFrequencyLimit:frequencyData]) {
            // 全局频控不满足
            return;
        }
        NSArray<NSDictionary *> *planList = x.responseObject[@"list"];
        [self foreachPopupInfoWithList:planList completed:^(NSDictionary *planData, NSDictionary *popupInfo) {
            if (planData && popupInfo) {
                // 有数据可以显示
                [self showAlertWithPlanData:planData popupInfo:popupInfo];
            }
        }];
    } error:^(NSError *error) {
        // ...
    }];
}

+ (void)foreachPopupInfoWithList:(NSArray<NSDictionary *> * const)planList
                       completed:(void(^)(NSDictionary *planData, NSDictionary *popupInfo))completedBlock {
    if (!planList.count) {
        completedBlock(nil, nil);
        return;
    }
    for (NSDictionary *planData in planList) {
        // 计划频控
        if (![self shouldShowWithPlanData:planData]) {
            continue;
        }
        // 素材频控
        NSArray<NSDictionary *> * const top_popups = planData[@"top_popups"];
        if (!top_popups.count) {
            continue;
        }
        NSInteger const exposure_days = [planData[@"expose_config"][@"exposure_days"] integerValue];
        for (NSDictionary *popupInfo in top_popups) {
            if ([self shouldShowWithPopupInfo:popupInfo exposureDays:exposure_days]) {
                // 满足计划和素材的双重频控要求
                completedBlock(planData, popupInfo);
                return;
            }
        }
    }
    // 兜底返回，无弹窗
    completedBlock(nil, nil);
}

+ (BOOL)shouldShowWithGlobalFrequencyLimit:(NSDictionary * const)frequencyData {
#ifdef DEBUG
    // 打开小火箭开关，则不用判断频控
    BOOL isIgnore = [[IMYKV defaultKV] boolForKey:@"#+IMYSubGuide_Limit_ignore"];
    if (isIgnore) {
        return YES;
    }
#endif
    
    NSInteger const daily_times = [frequencyData[@"daily_times"] integerValue];
    NSInteger const weekly_times = [frequencyData[@"weekly_times"] integerValue];
    NSInteger const monthly_times = [frequencyData[@"monthly_times"] integerValue];
    
    NSString * const kvKey = [NSString stringWithFormat:@"vip-popup-top-global-%@", [IMYPublicAppHelper shareAppHelper].userid];
    NSArray<NSNumber *> * const showedArray = [[IMYKV defaultKV] arrayForKey:kvKey];
    
    NSInteger dayCount = 0;
    NSInteger weekCount = 0;
    NSInteger monthCount = 0;
    
    for (NSNumber *time in showedArray.reverseObjectEnumerator) {
        // 每个都需要独立判断，不能使用 else if
        NSDate *date = [NSDate dateWithTimeIntervalSince1970:time.integerValue];
        if ([date isSameDayAsDate:NSDate.date]) {
            dayCount += 1;
        }
        if ([date isSameWeekAsDate:NSDate.date]) {
            weekCount += 1;
        }
        if ([date isSameMonthAsDate:NSDate.date]) {
            monthCount += 1;
        }
    }
    if (daily_times > 0 && dayCount >= daily_times) {
        // 今日曝光次数已达上限
        return NO;
    }
    if (weekly_times > 0 && weekCount >= weekly_times) {
        // 本周曝光次数已达上限
        return NO;
    }
    if (monthly_times > 0 && monthCount >= monthly_times) {
        // 本月曝光次数已达上限
        return NO;
    }
    // 全局频控通过
    return YES;
}

+ (BOOL)shouldShowWithPlanData:(NSDictionary * const)planData {
#ifdef DEBUG
    // 打开小火箭开关，则不用判断频控
    BOOL isIgnore = [[IMYKV defaultKV] boolForKey:@"#+IMYSubGuide_Limit_ignore"];
    if (isIgnore) {
        return YES;
    }
#endif
    
    // 是否允许曝光
    BOOL hasNeedShow = NO;
    
    // 获取历史曝光数据
    NSInteger const popupId = [planData[@"id"] integerValue];
    NSString * const kvKey = [NSString stringWithFormat:@"vip-popup-top-plan-%@-%ld", [IMYPublicAppHelper shareAppHelper].userid, popupId];
    
    NSArray<NSNumber *> *showedArray = [[IMYKV defaultKV] arrayForKey:kvKey];
    
    // 曝光过滤条件
    NSDictionary * const exposeConfig = planData[@"expose_config"];
    NSInteger const limitTimes = [exposeConfig[@"times"] integerValue];
    NSInteger const limitHours = [exposeConfig[@"interval_hours"] integerValue];
    NSString * const repeatFrequency = exposeConfig[@"repeat_frequency"];
        
    // 频次判断
    do {
        // 服务端不限制频控，每次进入必弹
        if (limitTimes == 0 && limitHours == 0) {
            hasNeedShow = YES;
            break;
        }
        
        NSInteger lastShowTime = [showedArray.lastObject integerValue];
        if (lastShowTime > 0 && repeatFrequency.length > 0) {
            NSDate * const lastDate = [NSDate dateWithTimeIntervalSince1970:lastShowTime];
            BOOL needResetDatas = NO;
            if ([repeatFrequency isEqualToString:@"weekly"]) {
                // 跨周了，清空曝光数据
                needResetDatas = ![lastDate isSameWeekAsDate:NSDate.date];
            } else if ([repeatFrequency isEqualToString:@"monthly"]) {
                // 跨月了，清空曝光数据
                needResetDatas = ![lastDate isSameMonthAsDate:NSDate.date];
            }
            // 跨周期了，需要清空曝光数据
            if (needResetDatas) {
                lastShowTime = 0;
                showedArray = nil;
                [[IMYKV defaultKV] removeForKey:kvKey];
            }
        }
        
        // 间隔时间
        if (limitHours > 0 && lastShowTime > 0) {
            NSInteger const nowTime = IMYDateTimeIntervalSince1970();
            NSInteger const lastDiffTime = nowTime - lastShowTime;
            NSInteger const diffHours = lastDiffTime / 3600;
            if (limitHours > diffHours) {
                break;
            }
        }
        // 为 0 则为无限弹
        if (limitTimes == 0) {
            hasNeedShow = YES;
            break;
        }
        
        // 判断总曝光次数(限制次数以内)
        hasNeedShow = (showedArray.count < limitTimes);
        
    } while (0);
    
    return hasNeedShow;
}

+ (BOOL)shouldShowWithPopupInfo:(NSDictionary * const)popupInfo
                   exposureDays:(NSInteger const)exposureDays {
#ifdef DEBUG
    // 打开小火箭开关，则不用判断频控
    BOOL isIgnore = [[IMYKV defaultKV] boolForKey:@"#+IMYSubGuide_Limit_ignore"];
    if (isIgnore) {
        return YES;
    }
#endif
    
    // 是否允许曝光
    BOOL hasNeedShow = NO;
    
    // 频次判断 (x天内只能弹一次)
    do {
        // 服务端不限制频控，每次进入必弹
        if (exposureDays == 0) {
            hasNeedShow = YES;
            break;
        }
        
        // 素材全局频控
        NSInteger const material_id = [popupInfo[@"vip_material_id"] integerValue];
        NSString * const kvKey = [NSString stringWithFormat:@"vip-popup-top-material-%@-%ld", [IMYPublicAppHelper shareAppHelper].userid, material_id];
        NSArray<NSNumber *> * const showedArray = [[IMYKV defaultKV] arrayForKey:kvKey];
        
        if (!showedArray.count) {
            // 无曝光数据，可直接显示
            hasNeedShow = YES;
            break;
        }
        
        // 频次判断 (x天内只能弹一次)
        NSInteger const lastShowTime = [showedArray.lastObject integerValue];
        NSDate * const lastShowDate = [NSDate dateWithTimeIntervalSince1970:lastShowTime];
        NSInteger const dayDiff = labs([lastShowDate distanceInDaysToDate:NSDate.date]);
        if (dayDiff >= exposureDays) {
            hasNeedShow = YES;
            break;
        }
        
    } while (0);
    
    return hasNeedShow;
}

+ (void)dissmissShowAlert {
    // 获取弹窗自动化机器人，并关闭弹窗
    IMYAlertShowAutoRobot *alertRobot = [IMYAlertShowAutoRobot createRobotWithKey:@"IMYMRHotUpQuickAlertView"];
    alertRobot.Dismiss();
}

+ (void)showAlertWithPlanData:(NSDictionary * const)planData popupInfo:(NSDictionary * const)popupInfo {
    // 获取弹窗自动化机器人
    IMYAlertShowAutoRobot *alertRobot = [IMYAlertShowAutoRobot createRobotWithKey:@"IMYMRHotUpQuickAlertView"];
    // 已有弹窗监控
    if (alertRobot.isReady) {
        return;
    }
    // 注册弹窗队列
    alertRobot.Priority(-2000).TabHomeType(SYTabBarIndexTypeHome).OnlyFirstly().IsBaseInvalid(^BOOL{
        // 查看push弹窗是否可显示
        return ![IMYAlertShowManager canShowWithTopPushStatus];
    }).CreateRealAlertView(^UIView<IMYAlertShowViewProtocol> *{
        IMYMRHotUpQuickAlertView *alertView = [IMYMRHotUpQuickAlertView new];
        alertView.planData = planData;
        alertView.popupInfo = popupInfo;
        return alertView;
    }).Ready();
}

- (void)saveShowCountWithPopupInfo {
    // 当前时间
    NSInteger const nowTime = IMYDateTimeIntervalSince1970();
    // 所有需要追加的曝光Key
    NSMutableArray * const allKeys = [NSMutableArray array];
    
    // 增加计划曝光数据
    {
        NSInteger const popupId = [self.planData[@"id"] integerValue];
        NSString * const kvKey = [NSString stringWithFormat:@"vip-popup-top-plan-%@-%ld", [IMYPublicAppHelper shareAppHelper].userid, popupId];
        [allKeys addObject:kvKey];
    }
    // 增加素材曝光数
    {
        NSInteger const material_id = [self.popupInfo[@"vip_material_id"] integerValue];
        NSString * const kvKey = [NSString stringWithFormat:@"vip-popup-top-material-%@-%ld", [IMYPublicAppHelper shareAppHelper].userid, material_id];
        [allKeys addObject:kvKey];
    }
    // 增加全局频控
    {
        NSString * const kvKey = [NSString stringWithFormat:@"vip-popup-top-global-%@", [IMYPublicAppHelper shareAppHelper].userid];
        [allKeys addObject:kvKey];
    }
    
    // 更新 IMYKV
    for (NSString *kvKey in allKeys) {
        NSArray<NSNumber *> * const showedArray = [[IMYKV defaultKV] arrayForKey:kvKey];
        NSMutableArray *mutableArray = [NSMutableArray arrayWithArray:showedArray];
        [mutableArray addObject:@(nowTime)];
        [[IMYKV defaultKV] setArray:mutableArray forKey:kvKey];
    }
}

- (void)show {
    if (self.superview) {
        return;
    }
    kCurrentShowing = YES;
    
    UIView *rootView = [UIApplication sharedApplication].delegate.window;
    [rootView addSubview:self];
    
    [self setupSubviews];
    
    self.alpha = 0;
    self.imy_top = 0;
    self.userInteractionEnabled = NO;
    
    [UIView animateWithDuration:0.2 animations:^{
        self.imy_top = kShowTopY;
        self.alpha = 1;
    } completion:^(BOOL finished) {
        // 允许点击
        self.userInteractionEnabled = YES;
    }];
    
    // 存储曝光数据
    [self saveShowCountWithPopupInfo];
    
    // 曝光埋点
    [self biReportWithAction:1];
    
    // 5秒后自动关闭
    @weakify(self);
    imy_asyncMainBlock(6, ^{
        @strongify(self);
        [self dismiss];
    });
}

- (void)didMoveToSuperview {
    [super didMoveToSuperview];
    // 显示状态做个兜底更新
    if (!self.superview && kCurrentShowing) {
        kCurrentShowing = NO;
    }
}

- (void)dismiss {
    if (!self.userInteractionEnabled) {
        return;
    }
    kCurrentShowing = NO;
    // 不允许点击
    self.userInteractionEnabled = NO;
    [UIView animateWithDuration:0.2 animations:^{
        self.imy_top -= kShowTopY;
        self.alpha = 0;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

- (void)setupSubviews {
    self.frame = CGRectMake(12, -100, SCREEN_WIDTH - 24, 100);
    [self imy_setBackgroundColor:kCK_White_AN];
    [self imy_drawAllCornerRadius:12];
    self.layer.shadowOpacity = 0.1;
    self.layer.shadowRadius = 3;
    self.layer.shadowOffset = CGSizeMake(0, 0);
    self.layer.shadowColor = [UIColor blackColor].CGColor;
    self.layer.masksToBounds = NO;
    
    // 上滑手势
    @weakify(self);
    UIPanGestureRecognizer *panGesture = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(handlePanGesture:)];
    [self addGestureRecognizer:panGesture];
    
    // 点击手势
    [self bk_whenTapped:^{
        @strongify(self);
        [self handleComfirmButtonEvent:self];
    }];
    
    NSString * const icon = self.popupInfo[@"icon"];
    NSString * const btn_text = self.popupInfo[@"btn_text"];
    CGFloat leftOffset = 0;
    CGFloat rightOffset = self.imy_width;
    
    // 图标
    if (icon.length > 0) {
        _iconView = [UIImageView new];
        _iconView.imy_size = CGSizeMake(36, 36);
        _iconView.imy_left = 12;
        [self addSubview:_iconView];
        
        // 加载图片
        [_iconView imy_setOriginalImageURL:icon];
        leftOffset = _iconView.imy_right;
    }
    
    // 按钮
    if (btn_text.length > 0) {
        _actionButton = [IMYTouchEXButton new];
        [_actionButton imy_drawAllCornerRadius:12];
        [_actionButton imy_setBackgroundColor:kCK_Red_A];
        _actionButton.userInteractionEnabled = NO;
        [_actionButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        _actionButton.titleLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
        [_actionButton setTitle:btn_text forState:UIControlStateNormal];
        [self addSubview:_actionButton];
        
        // 计算大小
        [_actionButton imy_sizeToFit];
        _actionButton.imy_height = 24;
        _actionButton.imy_width += 24;
        _actionButton.imy_right = self.imy_width - 12;
        
        rightOffset = _actionButton.imy_left;
    }
    
    NSString * const primary_title = self.popupInfo[@"primary_title"];
    NSString * const secondary_title = self.popupInfo[@"secondary_title"];
    
    // 主标题
    _titleLabel = [UILabel new];
    _titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
    [_titleLabel imy_setTextColor:kCK_Black_A];
    _titleLabel.numberOfLines = 1;
    _titleLabel.text = primary_title;
    _titleLabel.frame = CGRectMake(leftOffset + 12, 12, rightOffset - leftOffset - 24, 24);
    [self addSubview:_titleLabel];
    
    // 子标题
    _subtitleLabel = [UILabel new];
    _subtitleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
    [_subtitleLabel imy_setTextColor:kCK_Black_M];
    _subtitleLabel.numberOfLines = 2;
    _subtitleLabel.text = secondary_title;
    _subtitleLabel.frame = CGRectMake(leftOffset + 12, _titleLabel.imy_bottom + 4, rightOffset - leftOffset - 24, 100);
    [_subtitleLabel imy_sizeToFitHeight];
    if (_subtitleLabel.imy_height > 21) {
        _subtitleLabel.imy_height = 42;
    } else {
        _subtitleLabel.imy_height = 21;
    }
    [self addSubview:_subtitleLabel];
    
    // 修正整体位置
    self.imy_height = _subtitleLabel.imy_bottom + 12;
    _iconView.imy_centerY = self.imy_height / 2.0;
    _actionButton.imy_centerY = self.imy_height / 2.0;
}

- (void)handleComfirmButtonEvent:(id)sender {
    NSString * const btn_uri = self.popupInfo[@"btn_uri"];
    if (btn_uri.length > 0) {
        // 关闭自己
        [self dismiss];
        // 执行跳转协议
        [[IMYURIManager sharedInstance] runActionWithString:btn_uri];
    }
    
    // 点击埋点
    [self biReportWithAction:2];
}

- (void)handlePanGesture:(UIPanGestureRecognizer *)panGesture {
    // 更新弹窗位置
    CGPoint const translation = [panGesture translationInView:self];
    self.imy_top = MIN(kShowTopY, self.imy_top + translation.y);
    [panGesture setTranslation:CGPointZero inView:self];
    // 超过12pt:执行隐藏/弹回原位
    if (panGesture.state == UIGestureRecognizerStateEnded || panGesture.state == UIGestureRecognizerStateCancelled) {
        if (kShowTopY - self.imy_top > 10) {
            [self dismiss];
        } else {
            [UIView animateWithDuration:0.1 animations:^{
                self.imy_top = kShowTopY;
            }];
        }
    }
}

- (void)biReportWithAction:(NSInteger)action {
    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
    gaParams[@"event"] = @"dy_dyyxtc";
    gaParams[@"action"] = @(action);
    
    NSInteger const popupId = [self.planData[@"id"] integerValue];
    gaParams[@"info_id"] = @(popupId);
    
    NSInteger const material_id = [self.popupInfo[@"vip_material_id"] integerValue];
    gaParams[@"material_id"] = @(material_id);
    
    [IMYGAEventHelper postWithPath:@"event" params:gaParams headers:nil completed:nil];
}

static BOOL kCurrentShowing = NO;
+ (BOOL)isShowTopPushBar {
    return kCurrentShowing;
}

@end
